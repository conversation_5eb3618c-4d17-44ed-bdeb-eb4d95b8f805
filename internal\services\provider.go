package services

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"keyhub/internal/config"
	"keyhub/internal/models"

	"gorm.io/gorm"
)

type ProviderService struct {
	db *gorm.DB
}

func NewProviderService(db *gorm.DB) *ProviderService {
	return &ProviderService{db: db}
}

// GetAll 获取所有提供商（内置+自定义）
func (s *ProviderService) GetAll() ([]models.Provider, error) {
	var customProviders []models.Provider
	
	// 获取数据库中的自定义提供商
	err := s.db.Where("is_active = ?", true).Find(&customProviders).Error
	if err != nil {
		return nil, err
	}
	
	// 获取内置提供商
	builtinProviders := config.GetSupportedProviders()
	
	// 合并提供商列表
	allProviders := make([]models.Provider, 0, len(builtinProviders)+len(customProviders))
	
	// 添加内置提供商
	for _, builtin := range builtinProviders {
		provider := models.Provider{
			Name:            builtin.Name,
			Code:            builtin.Code,
			Color:           builtin.Color,
			Currency:        builtin.Currency,
			APIEndpoint:     builtin.APIEndpoint,
			Description:     builtin.Description,
			SupportBalance:  builtin.SupportBalance,
			BalanceEndpoint: builtin.BalanceEndpoint,
			SupportChat:     builtin.SupportChat,
			ChatEndpoint:    builtin.ChatEndpoint,
			ChatModel:       builtin.ChatModel,
			IsCustom:        false,
			IsActive:        true,
			AuthType:        "bearer",
			AuthHeader:      "Authorization",
		}
		allProviders = append(allProviders, provider)
	}
	
	// 添加自定义提供商
	allProviders = append(allProviders, customProviders...)
	
	return allProviders, nil
}

// GetByID 根据ID获取提供商
func (s *ProviderService) GetByID(id uint) (*models.Provider, error) {
	var provider models.Provider
	err := s.db.First(&provider, id).Error
	if err != nil {
		return nil, err
	}
	return &provider, nil
}

// GetByCode 根据代码获取提供商
func (s *ProviderService) GetByCode(code string) (*models.Provider, error) {
	// 先检查内置提供商
	builtinProviders := config.GetSupportedProviders()
	for _, builtin := range builtinProviders {
		if builtin.Code == code {
			provider := &models.Provider{
				Name:            builtin.Name,
				Code:            builtin.Code,
				Color:           builtin.Color,
				Currency:        builtin.Currency,
				APIEndpoint:     builtin.APIEndpoint,
				Description:     builtin.Description,
				SupportBalance:  builtin.SupportBalance,
				BalanceEndpoint: builtin.BalanceEndpoint,
				SupportChat:     builtin.SupportChat,
				ChatEndpoint:    builtin.ChatEndpoint,
				ChatModel:       builtin.ChatModel,
				IsCustom:        false,
				IsActive:        true,
				AuthType:        "bearer",
				AuthHeader:      "Authorization",
			}
			return provider, nil
		}
	}
	
	// 再检查自定义提供商
	var provider models.Provider
	err := s.db.Where("code = ? AND is_active = ?", code, true).First(&provider).Error
	if err != nil {
		return nil, err
	}
	return &provider, nil
}

// Create 创建新的提供商
func (s *ProviderService) Create(provider *models.Provider) error {
	// 检查代码是否已存在
	existingProvider, _ := s.GetByCode(provider.Code)
	if existingProvider != nil {
		return fmt.Errorf("提供商代码 '%s' 已存在", provider.Code)
	}
	
	// 设置默认值
	provider.IsCustom = true
	provider.IsActive = true
	provider.CreatedAt = time.Now()
	provider.UpdatedAt = time.Now()
	
	// 验证必填字段
	if err := s.validateProvider(provider); err != nil {
		return err
	}
	
	return s.db.Create(provider).Error
}

// Update 更新提供商
func (s *ProviderService) Update(id uint, updates *models.Provider) error {
	var provider models.Provider
	err := s.db.First(&provider, id).Error
	if err != nil {
		return err
	}
	
	// 不允许修改内置提供商
	if !provider.IsCustom {
		return fmt.Errorf("不能修改内置提供商")
	}
	
	// 如果修改了代码，检查是否冲突
	if updates.Code != "" && updates.Code != provider.Code {
		existingProvider, _ := s.GetByCode(updates.Code)
		if existingProvider != nil {
			return fmt.Errorf("提供商代码 '%s' 已存在", updates.Code)
		}
	}
	
	// 更新字段
	updates.UpdatedAt = time.Now()
	updates.IsCustom = true // 确保自定义标识不被修改
	
	// 验证更新后的数据
	if err := s.validateProvider(updates); err != nil {
		return err
	}
	
	return s.db.Model(&provider).Updates(updates).Error
}

// Delete 删除提供商
func (s *ProviderService) Delete(id uint) error {
	var provider models.Provider
	err := s.db.First(&provider, id).Error
	if err != nil {
		return err
	}
	
	// 不允许删除内置提供商
	if !provider.IsCustom {
		return fmt.Errorf("不能删除内置提供商")
	}
	
	// 检查是否有API Key在使用此提供商
	var count int64
	err = s.db.Model(&models.APIKey{}).Where("provider = ?", provider.Code).Count(&count).Error
	if err != nil {
		return err
	}
	
	if count > 0 {
		return fmt.Errorf("无法删除提供商，还有 %d 个API Key在使用", count)
	}
	
	// 软删除（设置为不活跃）
	return s.db.Model(&provider).Update("is_active", false).Error
}

// ValidateConnection 验证提供商连接
func (s *ProviderService) ValidateConnection(provider *models.Provider, testAPIKey string) error {
	if !provider.SupportChat {
		return fmt.Errorf("该提供商不支持连接测试")
	}
	
	// 构建测试请求
	url := strings.TrimSuffix(provider.APIEndpoint, "/") + provider.ChatEndpoint
	
	// 构建请求体
	requestBody := map[string]interface{}{
		"model": provider.ChatModel,
		"messages": []map[string]string{
			{
				"role":    "user",
				"content": "Hello",
			},
		},
		"max_tokens": 10,
	}
	
	bodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("构建请求体失败: %v", err)
	}
	
	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyBytes)))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}
	
	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	
	// 设置认证头
	if provider.AuthType == "bearer" {
		req.Header.Set(provider.AuthHeader, "Bearer "+testAPIKey)
	} else {
		req.Header.Set(provider.AuthHeader, testAPIKey)
	}
	
	// 解析额外的请求头
	if provider.BalanceHeaders != "" {
		var extraHeaders map[string]string
		if err := json.Unmarshal([]byte(provider.BalanceHeaders), &extraHeaders); err == nil {
			for key, value := range extraHeaders {
				req.Header.Set(key, value)
			}
		}
	}
	
	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 检查响应状态
	if resp.StatusCode >= 400 {
		return fmt.Errorf("API返回错误状态: %d", resp.StatusCode)
	}
	
	return nil
}

// validateProvider 验证提供商数据
func (s *ProviderService) validateProvider(provider *models.Provider) error {
	if provider.Name == "" {
		return fmt.Errorf("提供商名称不能为空")
	}
	
	if provider.Code == "" {
		return fmt.Errorf("提供商代码不能为空")
	}
	
	if provider.APIEndpoint == "" {
		return fmt.Errorf("API端点不能为空")
	}
	
	// 验证代码格式（只允许字母、数字、下划线、连字符）
	for _, char := range provider.Code {
		if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || 
			 (char >= '0' && char <= '9') || char == '_' || char == '-') {
			return fmt.Errorf("提供商代码只能包含字母、数字、下划线和连字符")
		}
	}
	
	return nil
}

// GetTemplates 获取提供商模板
func (s *ProviderService) GetTemplates() []models.ProviderTemplate {
	return models.GetProviderTemplates()
}
