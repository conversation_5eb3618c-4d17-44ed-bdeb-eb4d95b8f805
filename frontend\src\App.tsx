import { Routes, Route } from 'react-router-dom'
import { Layout, App as AntdApp } from 'antd'
import Sidebar from './components/Sidebar'
import TopNavbar from './components/TopNavbar'
import Dashboard from './pages/Dashboard'
import APIKeys from './pages/APIKeys'
import Providers from './pages/Providers'
import { useResponsive } from './hooks/useResponsive'
import './App.css'

const { Content, Header } = Layout

function App() {
  const isMobile = useResponsive(768)

  return (
    <AntdApp>
      <Layout style={{ minHeight: '100vh' }}>
        {isMobile ? (
          <>
            <Header style={{ padding: 0, background: '#001529', height: '48px', lineHeight: '48px' }}>
              <TopNavbar />
            </Header>
            <Content style={{ margin: '16px', padding: 16, background: '#fff' }}>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/apikeys" element={<APIKeys />} />
                <Route path="/providers" element={<Providers />} />
              </Routes>
            </Content>
          </>
        ) : (
          <>
            <Sidebar />
            <Layout>
              <Content style={{ margin: '16px', padding: 16, background: '#fff' }}>
                <Routes>
                  <Route path="/" element={<Dashboard />} />
                  <Route path="/apikeys" element={<APIKeys />} />
                  <Route path="/providers" element={<Providers />} />
                </Routes>
              </Content>
            </Layout>
          </>
        )}
      </Layout>
    </AntdApp>
  )
}

export default App
