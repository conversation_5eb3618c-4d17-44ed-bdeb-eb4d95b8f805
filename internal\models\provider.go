package models

import (
	"encoding/json"
	"time"
)

// Provider 表示一个AI服务提供商
type Provider struct {
	ID              uint      `json:"id" gorm:"primary_key"`
	Name            string    `json:"name" gorm:"not null"`             // 显示名称
	Code            string    `json:"code" gorm:"unique;not null"`      // 代码标识，必须唯一
	Color           string    `json:"color" gorm:"default:'blue'"`      // 前端显示颜色
	Currency        string    `json:"currency" gorm:"default:'USD'"`    // 币种：USD、CNY、TOKEN
	APIEndpoint     string    `json:"api_endpoint" gorm:"not null"`     // API端点
	Description     string    `json:"description"`                      // 描述
	SupportBalance  bool      `json:"support_balance" gorm:"default:false"` // 是否支持余额查询
	BalanceEndpoint string    `json:"balance_endpoint"`                 // 余额查询端点（如果支持）
	SupportChat     bool      `json:"support_chat" gorm:"default:true"` // 是否支持聊天测试
	ChatEndpoint    string    `json:"chat_endpoint" gorm:"default:'/v1/chat/completions'"` // 聊天API端点
	ChatModel       string    `json:"chat_model"`                       // 默认测试模型
	IsCustom        bool      `json:"is_custom" gorm:"default:true"`    // 是否为自定义提供商
	IsActive        bool      `json:"is_active" gorm:"default:true"`    // 是否启用
	
	// 认证相关
	AuthType        string    `json:"auth_type" gorm:"default:'bearer'"` // 认证类型：bearer, api_key
	AuthHeader      string    `json:"auth_header" gorm:"default:'Authorization'"` // 认证头名称
	
	// 余额查询相关配置
	BalanceMethod   string    `json:"balance_method" gorm:"default:'GET'"` // 余额查询HTTP方法
	BalanceHeaders  string    `json:"balance_headers"`                     // 额外的请求头（JSON格式）
	BalanceBodyTemplate string `json:"balance_body_template"`              // 请求体模板（如果需要）
	
	// 响应解析配置
	BalanceResponsePath string `json:"balance_response_path"` // 余额在响应中的JSON路径，如 "data.balance"
	TotalBalancePath    string `json:"total_balance_path"`    // 总余额路径
	GrantBalancePath    string `json:"grant_balance_path"`    // 赠送余额路径
	PaidBalancePath     string `json:"paid_balance_path"`     // 充值余额路径
	
	CreatedAt       time.Time `json:"-"`
	UpdatedAt       time.Time `json:"-"`
}

// MarshalJSON 自定义JSON序列化，将时间转换为时间戳
func (p Provider) MarshalJSON() ([]byte, error) {
	type Alias Provider

	// 创建一个匿名结构体用于序列化
	return json.Marshal(&struct {
		*Alias
		CreatedAt int64 `json:"created_at"`
		UpdatedAt int64 `json:"updated_at"`
	}{
		Alias:     (*Alias)(&p),
		CreatedAt: p.CreatedAt.UnixMilli(),
		UpdatedAt: p.UpdatedAt.UnixMilli(),
	})
}

// ProviderTemplate 提供商模板
type ProviderTemplate struct {
	Name            string `json:"name"`
	Description     string `json:"description"`
	APIEndpoint     string `json:"api_endpoint"`
	SupportBalance  bool   `json:"support_balance"`
	BalanceEndpoint string `json:"balance_endpoint"`
	SupportChat     bool   `json:"support_chat"`
	ChatEndpoint    string `json:"chat_endpoint"`
	ChatModel       string `json:"chat_model"`
	Currency        string `json:"currency"`
	Color           string `json:"color"`
	
	// 余额查询配置
	BalanceMethod       string `json:"balance_method"`
	BalanceHeaders      string `json:"balance_headers"`
	BalanceBodyTemplate string `json:"balance_body_template"`
	BalanceResponsePath string `json:"balance_response_path"`
	TotalBalancePath    string `json:"total_balance_path"`
	GrantBalancePath    string `json:"grant_balance_path"`
	PaidBalancePath     string `json:"paid_balance_path"`
	
	// 认证配置
	AuthType   string `json:"auth_type"`
	AuthHeader string `json:"auth_header"`
}

// GetProviderTemplates 获取预设的提供商模板
func GetProviderTemplates() []ProviderTemplate {
	return []ProviderTemplate{
		{
			Name:            "OpenAI 兼容接口",
			Description:     "兼容 OpenAI API 格式的服务商",
			APIEndpoint:     "https://api.example.com",
			SupportBalance:  false,
			SupportChat:     true,
			ChatEndpoint:    "/v1/chat/completions",
			ChatModel:       "gpt-3.5-turbo",
			Currency:        "USD",
			Color:           "green",
			AuthType:        "bearer",
			AuthHeader:      "Authorization",
		},
		{
			Name:            "One-API 转发",
			Description:     "One-API 格式的转发服务",
			APIEndpoint:     "https://api.example.com",
			SupportBalance:  true,
			BalanceEndpoint: "/v1/user/info",
			SupportChat:     true,
			ChatEndpoint:    "/v1/chat/completions",
			ChatModel:       "gpt-3.5-turbo",
			Currency:        "USD",
			Color:           "blue",
			BalanceMethod:   "GET",
			BalanceResponsePath: "data",
			TotalBalancePath:    "balance",
			AuthType:           "bearer",
			AuthHeader:         "Authorization",
		},
		{
			Name:            "New-API 转发",
			Description:     "New-API 格式的转发服务",
			APIEndpoint:     "https://api.example.com",
			SupportBalance:  true,
			BalanceEndpoint: "/v1/user/info",
			SupportChat:     true,
			ChatEndpoint:    "/v1/chat/completions",
			ChatModel:       "gpt-3.5-turbo",
			Currency:        "USD",
			Color:           "purple",
			BalanceMethod:   "GET",
			BalanceResponsePath: "data",
			TotalBalancePath:    "balance",
			AuthType:           "bearer",
			AuthHeader:         "Authorization",
		},
		{
			Name:            "Claude 兼容接口",
			Description:     "兼容 Claude API 格式的服务商",
			APIEndpoint:     "https://api.example.com",
			SupportBalance:  false,
			SupportChat:     true,
			ChatEndpoint:    "/v1/messages",
			ChatModel:       "claude-3-sonnet-20240229",
			Currency:        "USD",
			Color:           "orange",
			AuthType:        "api_key",
			AuthHeader:      "x-api-key",
		},
	}
}
