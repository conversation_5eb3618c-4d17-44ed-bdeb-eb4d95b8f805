package handlers

import (
	"net/http"
	"strconv"

	"keyhub/internal/models"
	"keyhub/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProviderHandler struct {
	service *services.ProviderService
}

func NewProviderHandler(db *gorm.DB) *ProviderHandler {
	return &ProviderHandler{
		service: services.NewProviderService(db),
	}
}

// GetAll 获取所有提供商
func (h *ProviderHandler) GetAll(c *gin.Context) {
	providers, err := h.service.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"providers": providers,
		"count":     len(providers),
	})
}

// GetByID 根据ID获取提供商
func (h *ProviderHandler) GetByID(c *gin.Context) {
	idStr := c.<PERSON>("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "无效的ID"})
		return
	}
	
	provider, err := h.service.GetByID(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "提供商不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}
	
	c.JSON(http.StatusOK, provider)
}

// Create 创建提供商
func (h *ProviderHandler) Create(c *gin.Context) {
	var provider models.Provider
	if err := c.ShouldBindJSON(&provider); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求数据格式错误: " + err.Error()})
		return
	}
	
	err := h.service.Create(&provider)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"message":  "提供商创建成功",
		"provider": provider,
	})
}

// Update 更新提供商
func (h *ProviderHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的ID"})
		return
	}
	
	var updates models.Provider
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求数据格式错误: " + err.Error()})
		return
	}
	
	err = h.service.Update(uint(id), &updates)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "提供商不存在"})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		}
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "提供商更新成功"})
}

// Delete 删除提供商
func (h *ProviderHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的ID"})
		return
	}
	
	err = h.service.Delete(uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "提供商不存在"})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		}
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "提供商删除成功"})
}

// ValidateConnection 验证提供商连接
func (h *ProviderHandler) ValidateConnection(c *gin.Context) {
	var request struct {
		Provider models.Provider `json:"provider"`
		APIKey   string          `json:"api_key"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求数据格式错误: " + err.Error()})
		return
	}
	
	if request.APIKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "测试API Key不能为空"})
		return
	}
	
	err := h.service.ValidateConnection(&request.Provider, request.APIKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"valid": false,
			"error": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "连接验证成功",
	})
}

// GetTemplates 获取提供商模板
func (h *ProviderHandler) GetTemplates(c *gin.Context) {
	templates := h.service.GetTemplates()
	c.JSON(http.StatusOK, gin.H{
		"templates": templates,
		"count":     len(templates),
	})
}

// CreateFromTemplate 从模板创建提供商
func (h *ProviderHandler) CreateFromTemplate(c *gin.Context) {
	var request struct {
		Template models.ProviderTemplate `json:"template"`
		Name     string                  `json:"name"`
		Code     string                  `json:"code"`
		Endpoint string                  `json:"endpoint"`
	}
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求数据格式错误: " + err.Error()})
		return
	}
	
	// 从模板创建提供商
	provider := models.Provider{
		Name:                request.Name,
		Code:                request.Code,
		Color:               request.Template.Color,
		Currency:            request.Template.Currency,
		APIEndpoint:         request.Endpoint,
		Description:         request.Template.Description,
		SupportBalance:      request.Template.SupportBalance,
		BalanceEndpoint:     request.Template.BalanceEndpoint,
		SupportChat:         request.Template.SupportChat,
		ChatEndpoint:        request.Template.ChatEndpoint,
		ChatModel:           request.Template.ChatModel,
		AuthType:            request.Template.AuthType,
		AuthHeader:          request.Template.AuthHeader,
		BalanceMethod:       request.Template.BalanceMethod,
		BalanceHeaders:      request.Template.BalanceHeaders,
		BalanceBodyTemplate: request.Template.BalanceBodyTemplate,
		BalanceResponsePath: request.Template.BalanceResponsePath,
		TotalBalancePath:    request.Template.TotalBalancePath,
		GrantBalancePath:    request.Template.GrantBalancePath,
		PaidBalancePath:     request.Template.PaidBalancePath,
	}
	
	err := h.service.Create(&provider)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"message":  "提供商创建成功",
		"provider": provider,
	})
}
