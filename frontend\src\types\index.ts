export interface APIKey {
  id: number
  name: string
  provider: string
  api_key: string
  description?: string
  created_at: number  // Unix时间戳(毫秒)
  updated_at: number  // Unix时间戳(毫秒)

  // 最新测试信息
  last_test_status?: boolean
  last_test_time?: number  // Unix时间戳(毫秒)
  last_response_time?: number
  last_test_error?: string

  // 最新余额信息
  total_balance?: number
  grant_balance?: number
  paid_balance?: number
  last_balance_check?: number  // Unix时间戳(毫秒)
}

// 提供商相关类型
export interface Provider {
  id?: number
  name: string
  code: string
  color: string
  currency: string
  api_endpoint: string
  description: string
  support_balance: boolean
  balance_endpoint?: string
  support_chat: boolean
  chat_endpoint?: string
  chat_model?: string
  is_custom: boolean
  is_active: boolean

  // 认证相关
  auth_type: string
  auth_header: string

  // 余额查询相关配置
  balance_method?: string
  balance_headers?: string
  balance_body_template?: string

  // 响应解析配置
  balance_response_path?: string
  total_balance_path?: string
  grant_balance_path?: string
  paid_balance_path?: string

  created_at?: number
  updated_at?: number
}

// 提供商模板类型
export interface ProviderTemplate {
  name: string
  description: string
  api_endpoint: string
  support_balance: boolean
  balance_endpoint?: string
  support_chat: boolean
  chat_endpoint?: string
  chat_model?: string
  currency: string
  color: string

  // 余额查询配置
  balance_method?: string
  balance_headers?: string
  balance_body_template?: string
  balance_response_path?: string
  total_balance_path?: string
  grant_balance_path?: string
  paid_balance_path?: string

  // 认证配置
  auth_type: string
  auth_header: string
}

// 任务相关类型
export interface Task {
  id: number
  type: 'test' | 'balance'
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  priority: number
  payload: string
  worker_id?: string
  started_at?: string
  completed_at?: string
  result?: string
  error_msg?: string
  retry_count: number
  max_retries: number
  next_retry_at?: string
  created_at: string
  updated_at: string
}

export interface QueueStats {
  pending: number
  running: number
  completed: number
  failed: number
  cancelled: number
}
