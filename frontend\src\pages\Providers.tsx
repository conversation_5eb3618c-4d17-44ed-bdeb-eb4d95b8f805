import React, { useState, useEffect } from 'react'
import { Table, Button, Space, Tag, Modal, Form, Input, Select, Popconfirm, Card, Switch, App, Toolt<PERSON>, Badge } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SettingOutlined,
  ApiOutlined,
  DollarOutlined,
  MessageOutlined,
  TemplateOutlined
} from '@ant-design/icons'
import { providerService } from '../services/api'
import { Provider, ProviderTemplate } from '../types'
import { useResponsive } from '../hooks/useResponsive'

const { Option } = Select
const { TextArea } = Input

const Providers: React.FC = () => {
  const { message } = App.useApp()
  const isMobile = useResponsive(768)
  
  const [providers, setProviders] = useState<Provider[]>([])
  const [templates, setTemplates] = useState<ProviderTemplate[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [templateModalVisible, setTemplateModalVisible] = useState(false)
  const [editingProvider, setEditingProvider] = useState<Provider | null>(null)
  const [form] = Form.useForm()
  const [templateForm] = Form.useForm()

  // 获取提供商列表
  const fetchProviders = async () => {
    setLoading(true)
    try {
      const response = await providerService.getAll()
      setProviders(response.data.providers || [])
    } catch (error) {
      message.error('获取提供商列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取模板列表
  const fetchTemplates = async () => {
    try {
      const response = await providerService.getTemplates()
      setTemplates(response.data.templates || [])
    } catch (error) {
      message.error('获取模板列表失败')
    }
  }

  useEffect(() => {
    fetchProviders()
    fetchTemplates()
  }, [])

  // 打开添加/编辑模态框
  const openModal = (provider?: Provider) => {
    setEditingProvider(provider || null)
    setModalVisible(true)
    if (provider) {
      form.setFieldsValue(provider)
    } else {
      form.resetFields()
      form.setFieldsValue({
        color: 'blue',
        currency: 'USD',
        auth_type: 'bearer',
        auth_header: 'Authorization',
        balance_method: 'GET',
        support_balance: false,
        support_chat: true,
        chat_endpoint: '/v1/chat/completions',
      })
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setEditingProvider(null)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      if (editingProvider) {
        await providerService.update(editingProvider.id!, values)
        message.success('提供商更新成功')
      } else {
        await providerService.create(values)
        message.success('提供商创建成功')
      }
      closeModal()
      fetchProviders()
    } catch (error: any) {
      message.error(error.response?.data?.error || '操作失败')
    }
  }

  // 删除提供商
  const handleDelete = async (id: number) => {
    try {
      await providerService.delete(id)
      message.success('提供商删除成功')
      fetchProviders()
    } catch (error: any) {
      message.error(error.response?.data?.error || '删除失败')
    }
  }

  // 打开模板选择模态框
  const openTemplateModal = () => {
    setTemplateModalVisible(true)
    templateForm.resetFields()
  }

  // 关闭模板模态框
  const closeTemplateModal = () => {
    setTemplateModalVisible(false)
    templateForm.resetFields()
  }

  // 从模板创建提供商
  const handleCreateFromTemplate = async (values: any) => {
    try {
      const selectedTemplate = templates.find(t => t.name === values.template)
      if (!selectedTemplate) {
        message.error('请选择模板')
        return
      }

      await providerService.createFromTemplate({
        template: selectedTemplate,
        name: values.name,
        code: values.code,
        endpoint: values.endpoint
      })
      
      message.success('提供商创建成功')
      closeTemplateModal()
      fetchProviders()
    } catch (error: any) {
      message.error(error.response?.data?.error || '创建失败')
    }
  }

  // 验证连接
  const handleValidateConnection = async (provider: Provider) => {
    const apiKey = prompt('请输入测试用的API Key:')
    if (!apiKey) return

    try {
      const response = await providerService.validateConnection(provider, apiKey)
      if (response.data.valid) {
        message.success('连接验证成功')
      } else {
        message.error(`连接验证失败: ${response.data.error}`)
      }
    } catch (error: any) {
      message.error(error.response?.data?.error || '验证失败')
    }
  }

  const columns: ColumnsType<Provider> = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Provider) => (
        <Space>
          <Tag color={record.color}>{name}</Tag>
          {!record.is_custom && <Badge count="内置" style={{ backgroundColor: '#52c41a' }} />}
        </Space>
      ),
    },
    {
      title: '代码',
      dataIndex: 'code',
      key: 'code',
      render: (code: string) => <code>{code}</code>,
    },
    {
      title: 'API端点',
      dataIndex: 'api_endpoint',
      key: 'api_endpoint',
      ellipsis: true,
    },
    {
      title: '功能',
      key: 'features',
      render: (_: any, record: Provider) => (
        <Space>
          {record.support_chat && (
            <Tooltip title="支持聊天测试">
              <MessageOutlined style={{ color: '#1890ff' }} />
            </Tooltip>
          )}
          {record.support_balance && (
            <Tooltip title="支持余额查询">
              <DollarOutlined style={{ color: '#52c41a' }} />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '币种',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency: string) => <Tag>{currency}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Provider) => (
        <Space size="small">
          {record.support_chat && (
            <Tooltip title="验证连接">
              <Button
                type="link"
                size="small"
                icon={<ApiOutlined />}
                onClick={() => handleValidateConnection(record)}
              />
            </Tooltip>
          )}
          {record.is_custom && (
            <>
              <Tooltip title="编辑">
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => openModal(record)}
                />
              </Tooltip>
              <Popconfirm
                title="确定要删除这个提供商吗？"
                onConfirm={() => handleDelete(record.id!)}
                okText="确定"
                cancelText="取消"
              >
                <Tooltip title="删除">
                  <Button
                    type="link"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Tooltip>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Card
        title="提供商管理"
        extra={
          <Space>
            <Button
              type="default"
              icon={<TemplateOutlined />}
              onClick={openTemplateModal}
            >
              从模板创建
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openModal()}
            >
              添加提供商
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchProviders}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={providers}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* 添加/编辑提供商模态框 */}
      <Modal
        title={editingProvider ? '编辑提供商' : '添加提供商'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="提供商名称"
            rules={[{ required: true, message: '请输入提供商名称' }]}
          >
            <Input placeholder="请输入提供商名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="提供商代码"
            rules={[
              { required: true, message: '请输入提供商代码' },
              { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和连字符' }
            ]}
          >
            <Input placeholder="请输入提供商代码（唯一标识）" disabled={!!editingProvider} />
          </Form.Item>

          <Form.Item
            name="api_endpoint"
            label="API端点"
            rules={[{ required: true, message: '请输入API端点' }]}
          >
            <Input placeholder="https://api.example.com" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={2} placeholder="请输入描述信息" />
          </Form.Item>

          <Space style={{ width: '100%' }} size="large">
            <Form.Item
              name="color"
              label="显示颜色"
            >
              <Select style={{ width: 120 }}>
                <Option value="blue">蓝色</Option>
                <Option value="green">绿色</Option>
                <Option value="red">红色</Option>
                <Option value="orange">橙色</Option>
                <Option value="purple">紫色</Option>
                <Option value="cyan">青色</Option>
                <Option value="magenta">品红</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="currency"
              label="币种"
            >
              <Select style={{ width: 100 }}>
                <Option value="USD">USD</Option>
                <Option value="CNY">CNY</Option>
                <Option value="TOKEN">TOKEN</Option>
              </Select>
            </Form.Item>
          </Space>

          <Form.Item
            name="support_chat"
            label="聊天测试"
            valuePropName="checked"
          >
            <Switch checkedChildren="支持" unCheckedChildren="不支持" />
          </Form.Item>

          <Form.Item noStyle dependencies={['support_chat']}>
            {({ getFieldValue }) => {
              const supportChat = getFieldValue('support_chat')
              return supportChat ? (
                <>
                  <Form.Item
                    name="chat_endpoint"
                    label="聊天API端点"
                    rules={[{ required: true, message: '请输入聊天API端点' }]}
                  >
                    <Input placeholder="/v1/chat/completions" />
                  </Form.Item>

                  <Form.Item
                    name="chat_model"
                    label="默认测试模型"
                  >
                    <Input placeholder="gpt-3.5-turbo" />
                  </Form.Item>
                </>
              ) : null
            }}
          </Form.Item>

          <Form.Item
            name="support_balance"
            label="余额查询"
            valuePropName="checked"
          >
            <Switch checkedChildren="支持" unCheckedChildren="不支持" />
          </Form.Item>

          <Form.Item noStyle dependencies={['support_balance']}>
            {({ getFieldValue }) => {
              const supportBalance = getFieldValue('support_balance')
              return supportBalance ? (
                <>
                  <Form.Item
                    name="balance_endpoint"
                    label="余额查询端点"
                    rules={[{ required: true, message: '请输入余额查询端点' }]}
                  >
                    <Input placeholder="/v1/user/info" />
                  </Form.Item>

                  <Form.Item
                    name="balance_method"
                    label="请求方法"
                  >
                    <Select>
                      <Option value="GET">GET</Option>
                      <Option value="POST">POST</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="balance_response_path"
                    label="余额响应路径"
                  >
                    <Input placeholder="data.balance" />
                  </Form.Item>
                </>
              ) : null
            }}
          </Form.Item>

          <Space style={{ width: '100%' }} size="large">
            <Form.Item
              name="auth_type"
              label="认证类型"
            >
              <Select style={{ width: 120 }}>
                <Option value="bearer">Bearer</Option>
                <Option value="api_key">API Key</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="auth_header"
              label="认证头"
            >
              <Input placeholder="Authorization" style={{ width: 150 }} />
            </Form.Item>
          </Space>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingProvider ? '更新' : '创建'}
              </Button>
              <Button onClick={closeModal}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 从模板创建模态框 */}
      <Modal
        title="从模板创建提供商"
        open={templateModalVisible}
        onCancel={closeTemplateModal}
        footer={null}
        width={600}
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={handleCreateFromTemplate}
        >
          <Form.Item
            name="template"
            label="选择模板"
            rules={[{ required: true, message: '请选择模板' }]}
          >
            <Select placeholder="请选择提供商模板">
              {templates.map(template => (
                <Option key={template.name} value={template.name}>
                  <div>
                    <strong>{template.name}</strong>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {template.description}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="name"
            label="提供商名称"
            rules={[{ required: true, message: '请输入提供商名称' }]}
          >
            <Input placeholder="请输入提供商名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="提供商代码"
            rules={[
              { required: true, message: '请输入提供商代码' },
              { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和连字符' }
            ]}
          >
            <Input placeholder="请输入提供商代码（唯一标识）" />
          </Form.Item>

          <Form.Item
            name="endpoint"
            label="API端点"
            rules={[{ required: true, message: '请输入API端点' }]}
          >
            <Input placeholder="https://api.example.com" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button onClick={closeTemplateModal}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Providers
