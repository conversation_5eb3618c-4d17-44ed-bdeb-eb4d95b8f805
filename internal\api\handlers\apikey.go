package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"keyhub/internal/config"
	"keyhub/internal/models"
	"keyhub/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type APIKeyHandler struct {
	service *services.APIKeyService
}

func NewAPIKeyHandler(db *gorm.DB) *APIKeyHandler {
	return &APIKeyHandler{
		service: services.NewAPIKeyService(db),
	}
}

// GetAll 获取所有API Key
func (h *APIKeyHandler) GetAll(c *gin.Context) {
	apiKeys, err := h.service.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, apiKeys)
}

// Create 创建新的API Key（支持单个和批量）
func (h *APIKeyHandler) Create(c *gin.Context) {
	var request struct {
		Name        string `json:"name" binding:"required"`
		Provider    string `json:"provider" binding:"required"`
		APIKey      string `json:"api_key" binding:"required"` // 单个或逗号分隔的多个Key
		Description string `json:"description"`
		IsBatch     bool   `json:"is_batch"` // 是否为批量模式
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数格式错误: " + err.Error()})
		return
	}

	// 验证提供商是否支持
	if config.GetProviderByCode(request.Provider) == nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的提供商"})
		return
	}

	if request.IsBatch {
		// 批量创建模式
		result, err := h.service.ParseAndCreateBatch(request.Name, request.Provider, request.APIKey, request.Description)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// 构建响应消息
		var messages []string
		if result.CreatedCount > 0 {
			messages = append(messages, fmt.Sprintf("成功创建 %d 个API Key", result.CreatedCount))
		}
		if result.SkippedCount > 0 {
			messages = append(messages, fmt.Sprintf("跳过 %d 个重复的Key", result.SkippedCount))
		}
		if result.InvalidCount > 0 {
			messages = append(messages, fmt.Sprintf("忽略 %d 个无效的Key", result.InvalidCount))
		}

		message := strings.Join(messages, "，")
		if message == "" {
			message = "没有处理任何API Key"
		}

		// 根据结果决定HTTP状态码
		statusCode := http.StatusCreated
		if result.CreatedCount == 0 {
			statusCode = http.StatusOK // 没有创建任何Key，但操作成功
		}

		c.JSON(statusCode, gin.H{
			"message": message,
			"result":  result,
		})
	} else {
		// 单个创建模式
		apiKey := models.APIKey{
			Name:        request.Name,
			Provider:    request.Provider,
			APIKey:      request.APIKey,
			Description: request.Description,
		}

		if err := h.service.Create(&apiKey); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "创建API Key失败: " + err.Error()})
			return
		}

		// 返回时隐藏真实的API Key
		if len(apiKey.APIKey) > 10 {
			apiKey.APIKey = apiKey.APIKey[:10] + "..."
		}
		c.JSON(http.StatusCreated, gin.H{
			"message": "API Key创建成功",
			"data":    apiKey,
		})
	}
}



// GetByID 根据ID获取API Key
func (h *APIKeyHandler) GetByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的ID格式"})
		return
	}

	apiKey, err := h.service.GetByID(uint(id))
	if err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "API Key不存在"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "查询API Key失败: " + err.Error()})
		}
		return
	}

	// 隐藏真实的API Key
	if len(apiKey.APIKey) > 10 {
		apiKey.APIKey = apiKey.APIKey[:10] + "..."
	}
	c.JSON(http.StatusOK, apiKey)
}

// Update 更新API Key
func (h *APIKeyHandler) Update(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的ID格式"})
		return
	}

	var updateData models.APIKey
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数格式错误: " + err.Error()})
		return
	}

	// 检查API Key是否存在
	existingKey, err := h.service.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "API Key不存在"})
		return
	}

	// 参数验证
	if updateData.Name != "" {
		existingKey.Name = updateData.Name
	}
	if updateData.Provider != "" {
		// 验证提供商
		if config.GetProviderByCode(updateData.Provider) == nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "不支持的提供商"})
			return
		}
		existingKey.Provider = updateData.Provider
	}
	if updateData.APIKey != "" {
		existingKey.APIKey = updateData.APIKey
	}
	if updateData.Description != "" {
		existingKey.Description = updateData.Description
	}

	if err := h.service.Update(existingKey); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新API Key失败: " + err.Error()})
		return
	}

	// 返回时隐藏真实的API Key
	if len(existingKey.APIKey) > 10 {
		existingKey.APIKey = existingKey.APIKey[:10] + "..."
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "API Key更新成功",
		"data":    existingKey,
	})
}

// Delete 删除API Key（支持单个和批量）
func (h *APIKeyHandler) Delete(c *gin.Context) {
	var request struct {
		IDs []uint `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请求参数格式错误: " + err.Error()})
		return
	}

	if len(request.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "请提供要删除的API Key ID列表"})
		return
	}

	// 判断是单个删除还是批量删除
	isBatch := len(request.IDs) > 1

	if isBatch {
		// 批量删除
		deletedCount, err := h.service.DeleteBatch(request.IDs)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "批量删除失败: " + err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": fmt.Sprintf("批量删除成功，删除了 %d 个API Key", deletedCount),
		})
	} else {
		// 单个删除
		id := request.IDs[0]

		// 检查API Key是否存在
		_, err := h.service.GetByID(id)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "API Key不存在"})
			return
		}

		if err := h.service.Delete(id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "删除API Key失败: " + err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "API Key删除成功",
		})
	}
}

// GetProviders 获取支持的提供商列表
func (h *APIKeyHandler) GetProviders(c *gin.Context) {
	providers := config.GetAllProviders()
	c.JSON(http.StatusOK, gin.H{
		"providers": providers,
	})
}
